package com.stpl.tech.pivot.service;

import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.pivot.domain.PaytmDQRCreateRequest;
import com.stpl.tech.pivot.domain.PaytmDQRStatusRequest;
import com.stpl.tech.pivot.domain.PaytmEdcCreateRequest;
import com.stpl.tech.pivot.domain.PaytmEdcStatusRequest;
import com.stpl.tech.pivot.domain.PaytmRefundRequest;

public interface PaytmPaymentService {
    PaytmEdcCreateRequest initiateEdcTransaction(OrderPaymentRequest orderPaymentRequest) throws Exception;

    PaytmDQRCreateRequest initiateDqrTransaction(OrderPaymentRequest orderPaymentRequest) throws Exception;

    Object updatePayment(PaytmEdcStatusRequest request, boolean skipSignatureVerification) throws Exception;

    Object updatePayment(PaytmDQRStatusRequest request, boolean skipSignatureVerification) throws Exception;

    Object refundPayment(PaytmRefundRequest request) throws Exception;
}
