package com.stpl.tech.pivot.service.impl;


import com.google.gson.Gson;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentRequestStatus;
import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import com.stpl.tech.pivot.core.payment.factory.PaymentFactory;
import com.stpl.tech.pivot.dao.OrderPaymentDetailRepository;
import com.stpl.tech.pivot.data.model.OrderPaymentDetailEntity;
import com.stpl.tech.pivot.domain.OrderPaymentDetail;
import com.stpl.tech.pivot.domain.PayPhiEdcCreateRequest;
import com.stpl.tech.pivot.domain.PayPhiEdcCreateResponse;
import com.stpl.tech.pivot.domain.PayPhiStatusRequest;
import com.stpl.tech.pivot.domain.PaytmEDCStatusResponse;
import com.stpl.tech.pivot.domain.PaytmEdcCreateRequest;
import com.stpl.tech.pivot.domain.PaytmEdcStatusRequest;
import com.stpl.tech.pivot.domain.PaytmRefundRequest;
import com.stpl.tech.pivot.domain.PaytmRefundResponse;
import com.stpl.tech.pivot.domain.mapper.OrderPaymentDetailMapper;
import com.stpl.tech.pivot.exceptions.BaseException;
import com.stpl.tech.pivot.service.PaymentService;
import com.stpl.tech.pivot.utils.ApplicationConstant;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import com.stpl.tech.pivot.domain.PaytmDQRCreateRequest;
import com.stpl.tech.pivot.domain.PaytmDQRStatusResponse;
import com.stpl.tech.pivot.domain.PaytmDQRStatusRequest;
import org.springframework.util.CollectionUtils;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Log4j2
public class PaymentServiceImpl implements PaymentService {
    @Autowired
    private PaymentFactory paymentFactory;
    @Autowired
    private OrderPaymentDetailRepository orderPaymentDetailRepository;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public PaytmEdcCreateRequest initiateTransactionRequest(OrderPaymentRequest order, PaymentPartnerType paymentPartnerType) throws Exception {
        return (PaytmEdcCreateRequest) paymentFactory.getCreatePaymentAdapter(paymentPartnerType)
                .createPaymentRequest(order, new HashMap<>());

    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public PayPhiEdcCreateRequest initiatePayphiTransactionRequest(OrderPaymentRequest order, PaymentPartnerType paymentPartnerType) throws Exception{
        return (PayPhiEdcCreateRequest) paymentFactory.getCreatePaymentAdapter(paymentPartnerType)
                .createPaymentRequest(order, new HashMap<>());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public PaytmDQRCreateRequest initiateDqrTransactionRequest(OrderPaymentRequest order, PaymentPartnerType paymentPartnerType) throws Exception {
        return (PaytmDQRCreateRequest) paymentFactory.getCreatePaymentAdapter(paymentPartnerType)
                .createPaymentRequest(order, new HashMap<>());

    }

    /**
     * @param transactionId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = true, propagation = Propagation.REQUIRED)
    public OrderPaymentDetail validateOrderPaymentDetail(String transactionId) {
        OrderPaymentDetailEntity orderPaymentDetail = orderPaymentDetailRepository.findByExternalOrderIdAndOrderIdNullAndRequestStatusNot(transactionId,
                PaymentRequestStatus.CANCELLED.name());
        if (Objects.isNull(orderPaymentDetail)) {
            throw new BaseException("Order Payment Not Found For the Transaction Id");
        }
        return OrderPaymentDetailMapper.INSTANCE.toDomain(orderPaymentDetail);
    }

    @Override
    public PaytmEDCStatusResponse updateTransactionRequest(PaytmEdcStatusRequest request, PaymentPartnerType paymentPartnerType) throws Exception {
        return (PaytmEDCStatusResponse) paymentFactory.getCreatePaymentAdapter(paymentPartnerType)
                .updatePayment(request, true);
    }

    @Override
    public PayPhiStatusRequest updatePayPhiTransactionRequest(PayPhiStatusRequest request, PaymentPartnerType paymentPartnerType) throws Exception {
        return (PayPhiStatusRequest) paymentFactory.getCreatePaymentAdapter(paymentPartnerType)
                .updatePayment(request, true);
    }

    @Override
    public PaytmDQRStatusResponse updateDqrTransactionRequest(PaytmDQRStatusRequest request, PaymentPartnerType paymentPartnerType) throws Exception {
        return (PaytmDQRStatusResponse) paymentFactory.getCreatePaymentAdapter(paymentPartnerType)
                .updatePayment(request, true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void updateTransactionStatus(Object response) throws Exception {
        try {
             log.info("Payload for patym webhook call ::: {}", new Gson().toJson(response));
//            if (Objects.nonNull(response)) {
//                OrderPaymentDetailEntity orderPaymentDetail = orderPaymentDetailRepository.findByPartnerOrderId(response.get(ApplicationConstant.ORDERID));
//                if (Objects.nonNull(orderPaymentDetail) && ApplicationConstant.DQR.equals(orderPaymentDetail.getPaymentModeName())) {
//                    if (ApplicationConstant.TXN_SUCCESS.equals(response.get(ApplicationConstant.STATUS))) {
//                        orderPaymentDetail.setPaymentStatus(ApplicationConstant.SUCCESSFUL);
//                        orderPaymentDetail.setPartnerPaymentStatus(response.get(ApplicationConstant.STATUS));
//                    }
//                    if (ApplicationConstant.TXN_FAILURE.equals(response.get(ApplicationConstant.STATUS))) {
//                        orderPaymentDetail.setPaymentStatus(ApplicationConstant.FAILED);
//                        orderPaymentDetail.setPartnerPaymentStatus(response.get(ApplicationConstant.STATUS));
//                    }
//                    orderPaymentDetailRepository.save(orderPaymentDetail);
//                }
//            }
        } catch (BaseException e) {
            log.error("Error while saving final transaction status by webhook call :::::: {}", e);
        }
    }

    public PaytmRefundResponse refundPayment(PaytmRefundRequest refundRequest, PaymentPartnerType paymentPartnerType) throws Exception {
        return (PaytmRefundResponse) paymentFactory.getCreatePaymentAdapter(paymentPartnerType)
                .refundPayment(refundRequest);
    }
}
