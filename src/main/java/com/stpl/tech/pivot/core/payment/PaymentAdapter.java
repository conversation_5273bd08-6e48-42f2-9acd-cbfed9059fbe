package com.stpl.tech.pivot.core.payment;

import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.pivot.exceptions.PaymentFailureException;

import java.util.Map;

public abstract class PaymentAdapter<T, V extends PaymentRequest> {

    public abstract V createPaymentRequest(T t, Map<String, String> map) throws Exception;

    public abstract Object updatePayment(Object object, boolean skipSignatureVerification) throws Exception;

    public Object refundPayment(Object object) throws Exception {
        throw new PaymentFailureException("Refund not supported for this payment partner");
    }
}
